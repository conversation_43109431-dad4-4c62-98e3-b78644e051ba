:root {
    --primary-color: #5B6AFF;
    --secondary-color: #8C54FF;
    --accent-color: #667EEA;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F5F7FA;
    --bg-card: #FFFFFF;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border-radius: 12px;
    --shadow: 0 4px 12px rgba(0,0,0,0.08);
    --spacing-unit: 8px;
}

[data-theme="dark"] {
    --primary-color: #5B6AFF;
    --secondary-color: #8C54FF;
    --accent-color: #667EEA;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-card: #252525;
    --text-primary: #FFFFFF;
    --text-secondary: #CCCCCC;
    --text-muted: #999999;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    margin: 0;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: 240px 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
}

.header {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 calc(var(--spacing-unit) * 3);
    background-color: var(--bg-primary);
    box-shadow: var(--shadow);
    z-index: 10;
}

.logo h1 {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.search-bar {
    flex-grow: 1;
    margin: 0 calc(var(--spacing-unit) * 4);
}

.search-bar input {
    width: 100%;
    max-width: 500px;
    padding: calc(var(--spacing-unit) * 1.5);
    border-radius: var(--border-radius);
    border: 1px solid var(--bg-secondary);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

#theme-toggle {
    padding: var(--spacing-unit) calc(var(--spacing-unit) * 2);
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.sidebar {
    grid-area: sidebar;
    background-color: var(--bg-primary);
    padding: calc(var(--spacing-unit) * 2);
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar nav ul li a {
    display: block;
    padding: calc(var(--spacing-unit) * 1.5);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
}

.sidebar nav ul li a.active,
.sidebar nav ul li a:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

.main-content {
    grid-area: main;
    overflow-y: auto;
    padding: calc(var(--spacing-unit) * 3);
}

.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: calc(var(--spacing-unit) * 3);
}

.tool-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: calc(var(--spacing-unit) * 3);
    box-shadow: var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-unit);
}

.card-title {
    font-size: 1.2rem;
    margin: 0 0 calc(var(--spacing-unit) * 1.5) 0;
    color: var(--text-primary);
}

.card-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: calc(var(--spacing-unit) * 2);
}

.card-tags {
    margin-bottom: calc(var(--spacing-unit) * 2);
}

.card-tags span {
    display: inline-block;
    background-color: var(--bg-secondary);
    color: var(--text-muted);
    padding: calc(var(--spacing-unit) * 0.5) var(--spacing-unit);
    border-radius: calc(var(--border-radius) / 2);
    font-size: 0.8rem;
    margin-right: var(--spacing-unit);
}

.card-action {
    width: 100%;
    padding: calc(var(--spacing-unit) * 1.5);
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.card-action:hover {
    background-color: var(--secondary-color);
}

@media (max-width: 768px) {
    .container {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
    }
    .sidebar {
        display: none;
    }
    .search-bar {
        display: none;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .tool-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
